{"name": "luminar-monorepo", "version": "1.0.0", "private": true, "description": "Luminar Architecture Optimization - Monorepo", "scripts": {"dev": "pnpm -r --parallel run dev", "dev:start": "node scripts/dev-start.js", "dev:parallel": "node scripts/dev-parallel.js", "dev:monitor": "node scripts/dev-monitor.js", "dev:health": "node scripts/dev-health-check.js", "dev:all": "node scripts/dev-parallel.js --monitor", "build": "pnpm -r run build", "build:all": "pnpm run build:packages && pnpm run build:apps", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:integration": "pnpm -r run test:integration", "test:e2e": "pnpm -r run test:e2e", "test:component": "pnpm --filter shared-ui test", "test:api": "pnpm --filter command-center test", "test:all": "pnpm run test && pnpm run test:integration && pnpm run test:e2e", "test:changed": "vitest run --changed", "lint": "pnpm -r run lint", "lint:fix": "pnpm -r run lint:fix", "format": "pnpm -r run format", "format:check": "pnpm -r run format:check", "typecheck": "pnpm -r run typecheck", "typecheck:all": "pnpm -r --parallel run typecheck", "clean": "pnpm -r run clean", "clean:all": "pnpm run clean && rm -rf node_modules", "start": "pnpm -r --parallel run start", "dev:apps": "pnpm --filter './apps/*' --parallel run dev", "build:apps": "pnpm --filter './apps/*' run build", "dev:packages": "pnpm --filter './packages/*' --parallel run dev", "build:packages": "pnpm --filter './packages/*' run build", "setup": "pnpm install && pnpm run build:packages", "prepare": "husky"}, "devDependencies": {"@playwright/test": "^1.54.1", "@testing-library/react": "^16.3.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.5.4", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "chalk": "^5.4.1", "cli-table3": "^0.6.5", "eslint": "^9.15.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "express": "^4.21.2", "happy-dom": "^18.0.1", "husky": "^9.1.7", "lint-staged": "^16.1.2", "msw": "^2.10.4", "ora": "^8.2.0", "playwright": "^1.54.1", "prettier": "^3.3.3", "typescript": "5.9.0-beta", "vitest": "^3.2.4", "ws": "^8.18.2"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@tanstack/react-router": "^1.127.0", "@tanstack/react-router-devtools": "^1.127.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.6"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0"}