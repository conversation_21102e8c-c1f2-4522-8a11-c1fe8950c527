import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { BullModule } from '@nestjs/bull';
import { HttpModule } from '@nestjs/axios';
import { AmnaService } from './amna.service';
import { AmnaController } from './amna.controller';
import { AmnaGateway } from './amna.gateway';

// Agent imports
import { AgentService } from './agents/agent.service';
import { AgentOrchestratorService } from './agents/agent-orchestrator.service';

// Task imports
import { TaskService } from './tasks/task.service';
import { TaskExecutorService } from './tasks/task-executor.service';
import { TaskSchedulerService } from './tasks/task-scheduler.service';

// Tool imports
import { ToolRegistryService } from './tools/tool-registry.service';
import { FunctionToolService } from './tools/function-tool.service';
import { McpToolService } from './tools/mcp-tool.service';
import { McpSerenaService } from './tools/mcp-serena.service';
import { McpMagicService } from './tools/mcp-magic.service';
import { McpPlaywrightService } from './tools/mcp-playwright.service';
import { McpMultifetchService } from './tools/mcp-multifetch.service';
import { McpSequentialService } from './tools/mcp-sequential.service';
import { McpContext7Service } from './tools/mcp-context7.service';
import { McpGoFastService } from './tools/mcp-gofast.service';
import { McpExecutorService } from './tools/mcp-executor.service';
import { McpHealthService } from './tools/mcp-health.service';
import { ToolConverterService } from './tools/tool-converter.service';
import { DynamicSchemaService } from './tools/dynamic-schema.service';
import { ToolPerformanceService } from './tools/tool-performance.service';

// LLM imports
import { LlmService } from './llm/llm.service';
import { OpenAiService } from './llm/openai.service';
import { OllamaService } from './llm/ollama.service';
import { StreamingHandlerService } from './llm/streaming-handler.service';

// Workflow imports
import { WorkflowService } from './workflows/workflow.service';
import { StatefulWorkflowEngine } from './workflows/stateful-workflow.engine';
import { ConditionalWorkflowEngine } from './workflows/conditional-workflow.engine';
import { WorkflowRealtimeGateway } from './workflows/workflow-realtime.gateway';
import { WorkflowPatternsService } from './workflows/workflow-patterns.service';
import { WorkflowStateService } from './workflows/workflow-state.service';

// Memory imports
import { MemoryStoreService } from './memory/memory-store.service';
import { ContextManagerService } from './memory/context-manager.service';
import { VectorMemoryService } from './memory/vector-memory.service';
import { SemanticMemoryService } from './memory/semantic-memory.service';
import { EmbeddingPipelineService } from './memory/embedding-pipeline.service';
import { MemoryClusteringService } from './memory/memory-clustering.service';

// Execution imports
import { ExecutionModule } from './execution/execution.module';
import { CodeExecutorService } from './execution/code-executor.service';
import { ExecutionSecurityService } from './execution/execution-security.service';
import { ExecutionMonitoringService } from './execution/execution-monitoring.service';

// Configuration
import { amnaConfig } from './config/amna.config';

@Global()
@Module({
  imports: [
    ConfigModule.forFeature(amnaConfig),
    HttpModule,
    BullModule.registerQueueAsync(
      ...['amna-agents', 'amna-tasks', 'amna-workflows', 'amna-embeddings', 'amna-clustering', 'amna-performance'].map(name => ({
        name,
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          redis: {
            host: configService.get('REDIS_HOST', 'localhost'),
            port: configService.get('REDIS_PORT', 6379),
            password: configService.get('REDIS_PASSWORD') || undefined,
          },
        }),
        inject: [ConfigService],
      }))
    ),
    ExecutionModule,
  ],
  controllers: [AmnaController],
  providers: [
    AmnaService,
    AmnaGateway,
    // Agent services
    AgentService,
    AgentOrchestratorService,
    // Task services
    TaskService,
    TaskExecutorService,
    TaskSchedulerService,
    // Tool services
    ToolRegistryService,
    FunctionToolService,
    McpToolService,
    McpSerenaService,
    McpMagicService,
    McpPlaywrightService,
    McpMultifetchService,
    McpSequentialService,
    McpContext7Service,
    McpGoFastService,
    McpExecutorService,
    McpHealthService,
    ToolConverterService,
    DynamicSchemaService,
    ToolPerformanceService,
    // LLM services
    LlmService,
    OpenAiService,
    OllamaService,
    StreamingHandlerService,
    // Workflow services
    WorkflowService,
    StatefulWorkflowEngine,
    ConditionalWorkflowEngine,
    WorkflowRealtimeGateway,
    WorkflowPatternsService,
    WorkflowStateService,
    // Memory services
    MemoryStoreService,
    ContextManagerService,
    VectorMemoryService,
    SemanticMemoryService,
    EmbeddingPipelineService,
    MemoryClusteringService,
    // Execution services
    CodeExecutorService,
    ExecutionSecurityService,
    ExecutionMonitoringService,
  ],
  exports: [
    AmnaService,
    AgentService,
    TaskService,
    ToolRegistryService,
    McpSerenaService,
    McpMagicService,
    McpPlaywrightService,
    McpMultifetchService,
    McpSequentialService,
    McpContext7Service,
    McpGoFastService,
    McpExecutorService,
    McpHealthService,
    DynamicSchemaService,
    ToolPerformanceService,
    LlmService,
    WorkflowService,
    StatefulWorkflowEngine,
    ConditionalWorkflowEngine,
    MemoryStoreService,
    SemanticMemoryService,
    EmbeddingPipelineService,
    MemoryClusteringService,
    CodeExecutorService,
    ExecutionSecurityService,
    ExecutionMonitoringService,
  ],
})
export class AmnaModule {}
