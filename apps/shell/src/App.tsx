import React, { Suspense } from 'react';
import { Router<PERSON>rovider, createRouter, createRoute, createRootRoute } from '@tanstack/react-router';
import { createLuminarRouter, getRouterConfig } from '@luminar/shared-ui';
import { Layout } from './components/Layout';
import { Dashboard } from './components/Dashboard';
import { LoadingSpinner } from './components/LoadingSpinner';
import { AppSelector } from './components/AppSelector';

// Create root route
const rootRoute = createRootRoute({
  component: Layout,
});

// Dashboard route
const dashboardRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/',
  component: Dashboard,
});

// Dynamic app routes
const amnaRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/amna/*',
  component: () => (
    <div className="p-8">
      <h2 className="text-2xl font-bold mb-4">AMNA</h2>
      <p>Navigate to <a href="http://localhost:5001" className="text-blue-500 hover:underline">http://localhost:5001</a> to access AMNA directly.</p>
    </div>
  ),
});

const lighthouseRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/lighthouse/*',
  component: () => (
    <div className="p-8">
      <h2 className="text-2xl font-bold mb-4">Lighthouse</h2>
      <p>Navigate to <a href="http://localhost:5003" className="text-blue-500 hover:underline">http://localhost:5003</a> to access Lighthouse directly.</p>
    </div>
  ),
});

const commandCenterRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/command-center/*',
  component: () => (
    <div className="p-8">
      <h2 className="text-2xl font-bold mb-4">Command Center</h2>
      <p>Command Center API is running at <a href="http://localhost:3000" className="text-blue-500 hover:underline">http://localhost:3000</a></p>
    </div>
  ),
});

const eConnectRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/e-connect/*',
  component: () => (
    <div className="p-8">
      <h2 className="text-2xl font-bold mb-4">E-Connect</h2>
      <p>Navigate to <a href="http://localhost:5002" className="text-blue-500 hover:underline">http://localhost:5002</a> to access E-Connect directly.</p>
    </div>
  ),
});

const trainingRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/training/*',
  component: () => (
    <div className="p-8">
      <h2 className="text-2xl font-bold mb-4">Training Analysis</h2>
      <p>Navigate to <a href="http://localhost:5005" className="text-blue-500 hover:underline">http://localhost:5005</a> to access Training Analysis directly.</p>
    </div>
  ),
});

const vendorsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/vendors/*',
  component: () => (
    <div className="p-8">
      <h2 className="text-2xl font-bold mb-4">Vendors</h2>
      <p>Navigate to <a href="http://localhost:5006" className="text-blue-500 hover:underline">http://localhost:5006</a> to access Vendors directly.</p>
    </div>
  ),
});

const winsRoute = createRoute({
  getParentRoute: () => rootRoute,
  path: '/wins/*',
  component: () => (
    <div className="p-8">
      <h2 className="text-2xl font-bold mb-4">Wins of Week</h2>
      <p>Navigate to <a href="http://localhost:5007" className="text-blue-500 hover:underline">http://localhost:5007</a> to access Wins of Week directly.</p>
    </div>
  ),
});

// Create the route tree
const routeTree = rootRoute.addChildren([
  dashboardRoute,
  amnaRoute,
  lighthouseRoute,
  commandCenterRoute,
  eConnectRoute,
  trainingRoute,
  vendorsRoute,
  winsRoute,
]);

// Create router using shared factory
const router = createLuminarRouter(routeTree, {
  ...getRouterConfig('default'),
  onRouteChange: (pathname: string) => {
    // Shell app specific route change tracking
    console.log('Shell route changed to:', pathname)
    
    // Track in analytics
    try {
      const analyticsStore = (window as any).__analyticsStore
      if (analyticsStore?.trackPageView) {
        analyticsStore.trackPageView(pathname, 'Luminar Shell Application')
      }
    } catch (error) {
      // Ignore analytics errors
    }
  },
  context: {
    app: 'shell',
    version: '1.0.0',
    features: ['module-federation', 'micro-frontends', 'routing']
  }
});

// Register router type
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}

function App() {
  const { isLoading, error } = useModuleFederation();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
          <h2 className="text-xl font-semibold text-red-800 mb-2">
            Failed to load configuration
          </h2>
          <p className="text-red-600">{error.message}</p>
        </div>
      </div>
    );
  }

  return (
    <Suspense fallback={<LoadingSpinner />}>
      <RouterProvider router={router} />
      <FeatureFlag flag="app-selector">
        <AppSelector />
      </FeatureFlag>
    </Suspense>
  );
}

export default App;