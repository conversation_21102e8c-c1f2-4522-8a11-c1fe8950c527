import React from 'react';
import { Link } from '@tanstack/react-router';
import { CheckCircle, XCircle, AlertCircle, Loader2 } from 'lucide-react';

interface AppStatus {
  name: string;
  displayName: string;
  path: string;
  remote: string;
}

const apps: AppStatus[] = [
  { name: 'amna', displayName: 'AMNA', path: '/amna', remote: 'amna' },
  { name: 'lighthouse', displayName: 'Lighthouse', path: '/lighthouse', remote: 'lighthouse' },
  { name: 'commandCenter', displayName: 'Command Center', path: '/command-center', remote: 'commandCenter' },
  { name: 'eConnect', displayName: 'E-Connect', path: '/e-connect', remote: 'eConnect' },
  { name: 'trainingAnalysis', displayName: 'Training Analysis', path: '/training', remote: 'trainingAnalysis' },
  { name: 'vendors', displayName: 'Vendors', path: '/vendors', remote: 'vendors' },
  { name: 'winsOfWeek', displayName: 'Wins of Week', path: '/wins', remote: 'winsOfWeek' },
];

function AppStatusCard({ app }: { app: AppStatus }) {
  // For now, assume all apps are available
  const isAvailable = true;
  const isChecking = false;

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">{app.displayName}</h3>
        <div className="flex items-center">
          {isChecking ? (
            <Loader2 className="w-5 h-5 text-gray-400 animate-spin" />
          ) : isAvailable ? (
            <CheckCircle className="w-5 h-5 text-green-500" />
          ) : isAvailable === false ? (
            <XCircle className="w-5 h-5 text-red-500" />
          ) : (
            <AlertCircle className="w-5 h-5 text-yellow-500" />
          )}
        </div>
      </div>
      <p className="text-sm text-gray-600 mb-4">
        Status: {isChecking ? 'Checking...' : isAvailable ? 'Online' : 'Offline'}
      </p>
      <Link
        to={app.path}
        className={`inline-flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
          isAvailable
            ? 'bg-blue-600 text-white hover:bg-blue-700'
            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
        }`}
        disabled={!isAvailable}
      >
        Open Application
      </Link>
    </div>
  );
}

export function Dashboard() {
  const { config, featureFlags } = useModuleFederation();

  return (
    <div>
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900">Luminar Platform Dashboard</h2>
        <p className="mt-2 text-gray-600">
          Micro-frontend architecture with Module Federation
        </p>
      </div>

      <div className="mb-8">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">System Information</h3>
        <div className="bg-white rounded-lg shadow p-6">
          <dl className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div>
              <dt className="text-sm font-medium text-gray-500">Environment</dt>
              <dd className="mt-1 text-lg font-semibold text-gray-900">
                {config.environment}
              </dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Total Apps</dt>
              <dd className="mt-1 text-lg font-semibold text-gray-900">{apps.length}</dd>
            </div>
            <div>
              <dt className="text-sm font-medium text-gray-500">Feature Flags</dt>
              <dd className="mt-1 text-lg font-semibold text-gray-900">
                {Object.keys(featureFlags).length} active
              </dd>
            </div>
          </dl>
        </div>
      </div>

      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Applications</h3>
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {apps.map((app) => (
            <AppStatusCard key={app.name} app={app} />
          ))}
        </div>
      </div>
    </div>
  );
}